 <?php include '../includes/header.php'; 
 if (!isset($_SESSION['userId']) || !isset($_SESSION['email'])) {
     header('Location: ../login');
     exit;
 }
 ?>
<body class="bg-dark-bg text-white font-audiowide h-screen flex">
    <!-- Sidebar -->
    <div class="w-80 bg-dark-card border-r border-gray-800 flex flex-col">
        <!-- Header -->
        <div class="p-6 border-b border-gray-800">
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center space-x-2">
                    <div class="text-red-accent text-2xl">✈️</div>
                    <span class="text-xl font-bold">TravelAI</span>
                </div>
                <button id="logoutBtn" class="text-gray-400 hover:text-red-accent transition-colors">
                    🚪
                </button>
            </div>
            <button id="newChatBtn" class="w-full py-3 bg-red-accent hover:bg-red-dark transition-colors rounded-lg font-bold">
                + New Chat
            </button>
        </div>

        <!-- Chat List -->
        <div class="flex-1 overflow-y-auto p-4">
            <div class="space-y-2" id="chatList">
                <!-- Chat items will be dynamically added here -->
            </div>
        </div>

        <!-- User Info -->
        <div class="p-4 border-t border-gray-800">
            <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-red-accent rounded-full flex items-center justify-center">
                    👤
                </div>
                <div>
                    <div class="font-bold text-sm" id="userName"><?php echo $_SESSION['email']; ?></div>
                    <div class="text-gray-400 text-xs" id="userEmail"><?php echo $_SESSION['email']; ?></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Chat Area -->
    <div class="flex-1 flex flex-col">
        <!-- Chat Header -->
        <div class="bg-dark-card border-b border-gray-800 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-xl font-bold" id="chatTitle">Travel Planning Assistant</h1>
                    <p class="text-gray-400 text-sm">Ask me anything about your travel plans</p>
                </div>
                <div class="flex space-x-2">
                    <button class="p-2 text-gray-400 hover:text-red-accent transition-colors">
                        🔄
                    </button>
                    <button class="p-2 text-gray-400 hover:text-red-accent transition-colors">
                        ⚙️
                    </button>
                </div>
            </div>
        </div>

        <!-- Messages Area -->
        <div class="flex-1 overflow-y-auto p-6" id="messagesArea">
            <div class="max-w-4xl mx-auto space-y-6" id="messagesList">
                <!-- Welcome message -->
                <div class="flex items-start space-x-4">
                    <div class="w-10 h-10 bg-red-accent rounded-full flex items-center justify-center flex-shrink-0">
                        🤖
                    </div>
                    <div class="bg-dark-card p-4 rounded-2xl rounded-tl-none max-w-2xl">
                        <p>Welcome to TravelAI! I'm your personal travel assistant. I can help you plan trips, find destinations, book accommodations, and answer any travel-related questions. How can I assist you today?</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Input Area -->
        <div class="bg-dark-card border-t border-gray-800 p-6">
            <div class="max-w-4xl mx-auto">
                <div class="flex space-x-4">
                    <div class="flex-1 relative">
                        <input type="text" id="messageInput" placeholder="Ask about destinations, flights, hotels, or travel tips..."
                               class="w-full px-6 py-4 bg-dark-bg border border-gray-700 rounded-2xl focus:border-red-accent focus:outline-none transition-colors pr-12">
                        <button id="sendBtn" class="absolute right-2 top-1/2 transform -translate-y-1/2 p-2 text-red-accent hover:text-red-dark transition-colors">
                            ➤
                        </button>
                    </div>
                </div>
                <div class="flex justify-center mt-4 space-x-4 text-sm text-gray-400">
                    <button class="hover:text-red-accent transition-colors">📍 Find Destinations</button>
                    <button class="hover:text-red-accent transition-colors">✈️ Flight Search</button>
                    <button class="hover:text-red-accent transition-colors">🏨 Hotels</button>
                    <button class="hover:text-red-accent transition-colors">🎯 Trip Planning</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Chat functionality
        let chats = [];
        let currentChatId = null;

        async function createNewChat() {
            try {
                const formData = new FormData();
                formData.append('title', 'New Travel Chat');

                const response = await fetch('api/createChat.php', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    await loadChats();
                    loadChat(data.chatId);
                    setActiveChat(data.chatId);
                } else {
                    console.error('Error creating chat:', data.message);
                }
            } catch (error) {
                console.error('Error creating chat:', error);
            }
        }

        async function loadChats() {
            try {
                const response = await fetch('api/getChats.php');
                const data = await response.json();

                if (data.success) {
                    chats = data.chats;
                    renderChatList();
                } else {
                    console.error('Error loading chats:', data.message);
                }
            } catch (error) {
                console.error('Error loading chats:', error);
            }
        }

        async function loadChat(chatId) {
            currentChatId = chatId;
            const chat = chats.find(c => c.id == chatId);
            if (chat) {
                document.getElementById('chatTitle').textContent = chat.title;
                await loadMessages(chatId);
                setActiveChat(chatId);
            }
        }

        async function loadMessages(chatId) {
            try {
                const response = await fetch(`api/getMessages.php?chatId=${chatId}`);
                const data = await response.json();

                if (data.success) {
                    renderMessages(data.messages);
                } else {
                    console.error('Error loading messages:', data.message);
                }
            } catch (error) {
                console.error('Error loading messages:', error);
            }
        }

        function setActiveChat(chatId) {
            // Remove active class from all chats
            const allChats = document.querySelectorAll('#chatList > div');
            allChats.forEach(chat => {
                chat.classList.remove('bg-red-accent');
                chat.classList.add('hover:bg-gray-800');
            });

            // Add active class to selected chat
            const activeChat = document.querySelector(`#chatList > div[data-chat-id="${chatId}"]`);
            if (activeChat) {
                activeChat.classList.remove('hover:bg-gray-800');
                activeChat.classList.add('bg-red-accent');
            }
        }

        function renderChatList() {
            const chatList = document.getElementById('chatList');
            chatList.innerHTML = '';

            chats.forEach(chat => {
                const chatItem = document.createElement('div');
                chatItem.setAttribute('data-chat-id', chat.id);
                chatItem.className = `p-3 rounded-lg cursor-pointer transition-colors ${
                    chat.id == currentChatId ? 'bg-red-accent' : 'hover:bg-gray-800'
                }`;
                chatItem.innerHTML = `
                    <div class="font-medium text-sm truncate">${chat.title}</div>
                    <div class="text-xs text-gray-400 mt-1">${new Date(chat.createdAt).toLocaleDateString()}</div>
                `;
                chatItem.onclick = () => {
                    loadChat(chat.id);
                    setActiveChat(chat.id);
                };
                chatList.appendChild(chatItem);
            });
        }

        function renderMessages(messages) {
            const messagesList = document.getElementById('messagesList');
            messagesList.innerHTML = `
                <div class="flex items-start space-x-4">
                    <div class="w-10 h-10 bg-red-accent rounded-full flex items-center justify-center flex-shrink-0">
                        🤖
                    </div>
                    <div class="bg-dark-card p-4 rounded-2xl rounded-tl-none max-w-2xl">
                        <p>Welcome to TravelAI! I'm your personal travel assistant. I can help you plan trips, find destinations, book accommodations, and answer any travel-related questions. How can I assist you today?</p>
                    </div>
                </div>
            `;

            messages.forEach(message => {
                const messageDiv = document.createElement('div');
                messageDiv.className = 'flex items-start space-x-4';
                
                if (message.type === 'user') {
                    messageDiv.innerHTML = `
                        <div class="flex-1"></div>
                        <div class="bg-red-accent p-4 rounded-2xl rounded-tr-none max-w-2xl">
                            <p>${message.content}</p>
                        </div>
                        <div class="w-10 h-10 bg-gray-600 rounded-full flex items-center justify-center flex-shrink-0">
                            👤
                        </div>
                    `;
                } else {
                    messageDiv.innerHTML = `
                        <div class="w-10 h-10 bg-red-accent rounded-full flex items-center justify-center flex-shrink-0">
                            🤖
                        </div>
                        <div class="bg-dark-card p-4 rounded-2xl rounded-tl-none max-w-2xl">
                            <p>${message.content}</p>
                        </div>
                    `;
                }
                
                messagesList.appendChild(messageDiv);
            });

            // Scroll to bottom
            document.getElementById('messagesArea').scrollTop = document.getElementById('messagesArea').scrollHeight;
        }

        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();

            if (!message) return;

            if (!currentChatId) {
                await createNewChat();
                if (!currentChatId) return;
            }

            try {
                // Send user message
                const formData = new FormData();
                formData.append('chatId', currentChatId);
                formData.append('messageType', 'user');
                formData.append('content', message);

                const response = await fetch('api/sendMessage.php', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    input.value = '';
                    await loadMessages(currentChatId);
                    await loadChats(); // Refresh chat list to update timestamps

                    // Get AI response from the server
                    const aiFormData = new FormData();
                    aiFormData.append('chatId', currentChatId);
                    aiFormData.append('messageType', 'ai');
                    aiFormData.append('content', message); // Send original message to get AI response

                    const aiResponse = await fetch('api/sendMessage.php', {
                        method: 'POST',
                        body: aiFormData
                    });

                    const aiData = await aiResponse.json();

                    if (aiData.success) {
                        // The response from the server should contain the AI's message
                        const serverResponse = aiData.response || "I apologize, but I couldn't process that request.";
                        
                        // Send the actual AI response back to be stored
                        const finalAiFormData = new FormData();
                        finalAiFormData.append('chatId', currentChatId);
                        finalAiFormData.append('messageType', 'ai');
                        finalAiFormData.append('content', serverResponse);

                        const finalAiResponse = await fetch('api/sendMessage.php', {
                            method: 'POST',
                            body: finalAiFormData
                        });

                        const finalAiData = await finalAiResponse.json();

                        if (finalAiData.success) {
                            await loadMessages(currentChatId);
                            await loadChats(); // Refresh chat list
                        }
                    }
                } else {
                    console.error('Error sending message:', data.message);
                }
            } catch (error) {
                console.error('Error sending message:', error);
            }
        }

        // Event listeners
        document.getElementById('newChatBtn').addEventListener('click', createNewChat);
        document.getElementById('sendBtn').addEventListener('click', sendMessage);
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        document.getElementById('logoutBtn').addEventListener('click', function() {
            fetch('api/logout.php')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.location.href = '../login';
                } else {
                    console.error('Logout failed:', data.message);
                }
            })
            .catch(error => {
                console.error('Error during logout:', error);
            });
        });

        // Initialize
        async function initialize() {
            await loadChats();
            if (chats.length === 0) {
                await createNewChat();
            } else {
                loadChat(chats[0].id);
            }
        }

        // Start the application
        initialize();
    </script>
<?php include '../includes/footer.php'; ?>