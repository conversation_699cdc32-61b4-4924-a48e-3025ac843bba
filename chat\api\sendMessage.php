<?php
require '../../includes/config.php';

// Check if user is logged in
if (!isset($_SESSION['userId'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$userId = $_SESSION['userId'];
$chatId = isset($_POST['chatId']) ? intval($_POST['chatId']) : 0;
$messageType = isset($_POST['messageType']) ? $_POST['messageType'] : 'user';
$content = isset($_POST['content']) ? $_POST['content'] : '';

// Validate inputs
if ($chatId <= 0) {
    echo json_encode(['success' => false, 'message' => 'Invalid chat ID']);
    exit;
}

if (empty($content)) {
    echo json_encode(['success' => false, 'message' => 'Message content cannot be empty']);
    exit;
}

if (!in_array($messageType, ['user', 'ai'])) {
    echo json_encode(['success' => false, 'message' => 'Invalid message type']);
    exit;
}

// Sanitize input
$content = filter_var($content, FILTER_SANITIZE_STRING);
// Verify that the chat belongs to the user
$chatQuery = "SELECT id, title FROM chats WHERE id = ? AND user_id = ?";
$chatStmt = $db->prepare($chatQuery);
$chatStmt->bind_param("ii", $chatId, $userId);
$chatStmt->execute();
$chatResult = $chatStmt->get_result();

if ($chatResult->num_rows === 0) {
    echo json_encode(['success' => false, 'message' => 'Chat not found or access denied']);
    exit;
}

$chat = $chatResult->fetch_assoc();

// Insert the message
$query = "INSERT INTO messages (chat_id, user_id, message_type, content) VALUES (?, ?, ?, ?)";
$stmt = $db->prepare($query);
$stmt->bind_param("iiss", $chatId, $userId, $messageType, $content);

if ($stmt->execute()) {
    $messageId = $db->insert_id;
    
    // Update chat's updated_at timestamp
    $updateChatQuery = "UPDATE chats SET updated_at = CURRENT_TIMESTAMP WHERE id = ?";
    $updateStmt = $db->prepare($updateChatQuery);
    $updateStmt->bind_param("i", $chatId);
    $updateStmt->execute();
    
    // If this is the first user message, update the chat title
    if ($messageType === 'user') {
        $countQuery = "SELECT COUNT(*) as count FROM messages WHERE chat_id = ? AND message_type = 'user'";
        $countStmt = $db->prepare($countQuery);
        $countStmt->bind_param("i", $chatId);
        $countStmt->execute();
        $countResult = $countStmt->get_result();
        $count = $countResult->fetch_assoc()['count'];
        
        if ($count == 1) { // First user message
            $newTitle = strlen($content) > 30 ? substr($content, 0, 30) . '...' : $content;
            $titleQuery = "UPDATE chats SET title = ? WHERE id = ?";
            $titleStmt = $db->prepare($titleQuery);
            $titleStmt->bind_param("si", $newTitle, $chatId);
            $titleStmt->execute();
            $titleStmt->close();
        }
        $countStmt->close();
    }
    
    if ($messageType === 'ai') {
        // Array of possible AI responses
        $responses = [
            "That  like an exciting travel destination! Would you like specific recommendations for activities or accommodations?",
            "I'd be  to help plan your trip. What specific aspects would you like to know more about?",
            "Great ! Have you considered the best time of year to visit this location?",
            "I  help you create an itinerary. What's your preferred travel style - adventurous, relaxed, or cultural?",
            "That's  interesting destination! Would you like to know about local customs and traditions?"
        ];
        
        // Get random response
        $randomResponse = $responses[array_rand($responses)];
        
        echo json_encode([
            'success' => true, 
            'messageId' => $messageId,
            'timestamp' => date('Y-m-d H:i:s'),
            'response' => $randomResponse
        ]);
    } else {
        echo json_encode([
            'success' => true, 
            'messageId' => $messageId,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }
    
    $updateStmt->close();
} else {
    echo json_encode(['success' => false, 'message' => 'Error saving message']);
}

$stmt->close();
$chatStmt->close();
?>
